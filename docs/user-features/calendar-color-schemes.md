# Calendar Color Schemes

The calendar interface now supports two different color schemes for displaying reservations, allowing users to view the data in the most meaningful way for their needs.

## Color Scheme Options

### 1. Color by Status (Default)
Reservations are colored based on their booking status:
- **Yellow (#fbbf24)** - Pending reservations
- **Light Blue (#23b7e5)** - Confirmed reservations  
- **<PERSON> (#8c9097)** - Completed or past reservations

### 2. Color by Field
Reservations are colored based on which field they are booked for:
- **<PERSON> (#4eac4c)** - Field 1
- **<PERSON> (#858585)** - Field 2
- **<PERSON> (#c0a681)** - Field 3
- **Dark Yellow (#D68910)** - Field 4
- **Dark Blue (#2980B9)** - Field 5
- **<PERSON><PERSON> (#06b6d4)** - Field 6
- **Orange (#f97316)** - Field 7
- **<PERSON>e (#84cc16)** - Field 8

*Note: Colors cycle through the palette for additional fields using the formula `(field_id - 1) % 8`*

## Features

### Color Scheme Selector
- Located in the top-right of the calendar legend area with palette icon
- Dropdown with two options: "Status" and "Field"
- Changes take effect immediately when selected
- Triggers calendar event refresh to apply new color scheme

### Dynamic Legend
- Updates automatically based on the selected color scheme via `updateLegend()` method
- Shows relevant color meanings for the current scheme
- Positioned prominently above the calendar in a Bootstrap card with light background
- Status scheme shows: Pending (yellow), Confirmed (light blue), Completed (gray)
- Field scheme dynamically generates legend items for all available fields
- Uses Bootstrap badge components with inline styles for color display

### Persistent Selection
- User's color scheme preference is saved in browser localStorage
- Selection persists across browser sessions
- Automatically restored when returning to the calendar

### Past Reservations
**Color by Status scheme:**
- Past reservations are shown in muted gray (#8c9097) regardless of original status
- Prioritizes status-based information over field identification

**Color by Field scheme:**
- Past reservations retain their field-specific colors (blue, red, green, etc.)
- 50% transparency is applied to visually distinguish them from current/future reservations
- Subtle diagonal pattern overlay provides additional visual distinction
- Allows users to see historical field usage patterns while clearly identifying past bookings

## Color Palettes

### Status Color Palette
The following colors are used for status-based coloring:
```javascript
statusColors: {
    'Pending': '#fbbf24',     // yellow
    'Confirmed': '#23b7e5',   // light blue
    'Cancelled': '#ef4444',   // red
    'Complete': '#8c9097',    // gray
    'Completed': '#8c9097',   // gray
    'past': '#8c9097'         // gray for past reservations
}
```

### Field Color Palette
The following 8-color palette is used for field-based coloring:
```javascript
fieldColors = [
    '#4eac4c', // green
    '#858585', // grey
    '#c0a681', // light brown
    '#D68910', // dark yellow
    '#2980B9', // dark blue
    '#06b6d4', // cyan
    '#f97316', // orange
    '#84cc16', // lime
];
```

## Technical Implementation

### Frontend
- JavaScript `ColorSchemeManager` object handles scheme switching and persistence
- Dynamic legend updates without page refresh using `updateLegend()` method
- localStorage integration for preference persistence with key `calendar-color-scheme`
- Field color mapping uses `(field_id - 1) % 8` formula for consistency
- Color palettes defined in `fieldColors` and `statusColors` objects
- Event listener on color scheme selector triggers calendar refresh via `calendar.refetchEvents()`
- DOM elements cached in `DOMCache` object for performance
- Initialization occurs after calendar rendering via `ColorSchemeManager.init()`

### Backend
- `CalendarController::events()` accepts `color_scheme` parameter (defaults to 'status')
- Server-side color determination based on scheme selection in event generation loop
- Field-based coloring uses consistent 8-color palette array
- Color assignment formula: `($reservation->field_id - 1) % count($fieldColors)` ensures consistent mapping
- Status-based coloring uses match expressions for color assignment
- Past reservations handled differently per scheme (gray for status, original colors for field)

### Color Consistency
- Both frontend legend and backend events use identical color mapping logic
- Field ID to color index mapping: `(field_id - 1) % 8`
- Ensures legend colors always match calendar event colors
- Supports field IDs of any value (not just sequential 1,2,3...)

### Visual Styling for Past Reservations
**Field-based color scheme:**
- Past reservations use CSS class `past-reservation-scheme`
- 50% opacity (`opacity: 0.5`) applied to the entire event
- Diagonal stripe pattern overlay for additional visual distinction using `::after` pseudo-element
- Pattern uses `repeating-linear-gradient` with 45-degree diagonal stripes
- Pattern adapts to light/dark theme modes:
  - Light mode: `rgba(255, 255, 255, 0.1)` stripes
  - Dark mode: `rgba(0, 0, 0, 0.2)` stripes
- Maintains field color while clearly indicating past status

**Status-based color scheme:**
- Past reservations use standard gray color (#8c9097)
- No transparency effects needed since color change is sufficient
- Consistent with traditional status-priority approach

### CSS Classes and Event Mounting
**Event mounting process:**
- FullCalendar's `eventDidMount` callback adds CSS classes to past reservations
- Past reservations receive both `past-reservation` and `past-reservation-scheme` classes
- Tooltip is added with reservation details including past indicator

**CSS styling for borders:**
- Specific border colors are applied based on background colors for better contrast
- Pending events (yellow): `rgba(217, 119, 6, 0.6)` border
- Confirmed events (light blue): `rgba(14, 116, 144, 0.6)` border
- Completed/Past events (gray): `rgba(75, 85, 99, 0.6)` border

### API Parameters
The calendar events endpoint accepts the following parameters:
- `color_scheme=status` - Use status-based colors (default)
- `color_scheme=field` - Use field-based colors
- `start` - Start date for event range (ISO format)
- `end` - End date for event range (ISO format)
- `field_id` - Optional field filter

### Event Data Structure
Each calendar event includes the following properties:
```javascript
{
    id: reservation.id,
    title: 'Field Name - Customer Name',
    start: 'YYYY-MM-DDTHH:mm:ss',
    end: 'YYYY-MM-DDTHH:mm:ss',
    color: '#hexcolor',
    textColor: '#ffffff',
    extendedProps: {
        reservation_id: reservation.id,
        field_id: reservation.field_id,
        field_name: 'Field Name',
        customer_name: 'Customer Name',
        status: 'Pending|Confirmed|Cancelled|Complete',
        total_cost: 'formatted_cost',
        duration: hours,
        can_edit: boolean,
        special_requests: 'text',
        is_past: boolean,
        color_scheme: 'status|field'
    },
    url: '/reservations/{id}'
}
```

## Usage Examples

### Viewing by Status
Ideal for:
- Tracking reservation approval workflow
- Identifying pending reservations that need attention
- Monitoring booking status distribution

### Viewing by Field
Ideal for:
- Visualizing field utilization patterns
- Identifying busy vs. underutilized fields
- Planning field maintenance schedules
- Balancing bookings across facilities

## Browser Compatibility
- Works in all modern browsers supporting ES6+ features
- Requires localStorage support for preference persistence
- Graceful fallback to status-based coloring if localStorage is unavailable
- Uses CSS features: `repeating-linear-gradient`, `::after` pseudo-elements, CSS custom properties
- No additional dependencies required beyond FullCalendar and existing framework
