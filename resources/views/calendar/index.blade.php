@extends('layouts.admin')

@section('title', 'Reservations Calendar - SMP Online')

@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Reservation Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Calendar</li>
                </ol>
            </nav>
        </div>
    </div>

    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Page Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Calendar Card -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="bx bx-calendar-alt me-2"></i>Reservations Calendar
                    </div>
                    <div class="d-flex gap-2 align-items-center">
                        <a href="{{ route('reservations.create') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-plus me-1"></i>New Reservation
                        </a>
                        <a href="{{ route('reservations.index') }}" class="btn btn-success btn-sm">
                            <i class="ti ti-list me-1"></i>View List
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    <!-- Legend and Controls -->
                    <div class="calendar-legend mb-4 p-3 bg-light rounded">
                        <div class="d-flex justify-content-between align-items-start flex-wrap gap-3">
                            <!-- Dynamic Legend -->
                            <div class="flex-grow-1">
                                <h6 class="fw-semibold mb-2 text-muted">Legend:</h6>
                                <div id="legendContent" class="d-flex flex-wrap gap-3 fs-12">
                                    <!-- Status-based legend (default) -->
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-warning me-2">&nbsp;</span>
                                        <span>Pending</span>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-secondary me-2">&nbsp;</span>
                                        <span>Confirmed</span>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge" style="background-color: #8c9097;">&nbsp;</span>
                                        <span class="ms-2">Completed (Past Reservations)</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Color Scheme Selector -->
                            <div class="flex-shrink-0">
                                <h6 class="fw-semibold mb-2 text-muted"><i class="ti ti-palette me-1"></i>Color by:</h6>
                                <select id="colorSchemeSelector" class="form-select form-select-sm fs-12" style="min-width: 150px;">
                                    <option value="status">Status</option>
                                    <option value="field">Field</option>
                                </select>
                            </div>

                            <!-- Field Filter -->
                            <div class="flex-shrink-0">
                                <h6 class="fw-semibold mb-2 text-muted"><i class="ti ti-filter me-1"></i>Filter by field:</h6>
                                <select id="fieldFilter" class="form-select form-select-sm fs-12" style="min-width: 150px;">
                                    <option value="">All Fields</option>
                                    @foreach ($fields as $field)
                                        <option value="{{ $field->id }}">{{ $field->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Loading State -->
                    <div id="calendarLoading" class="text-center py-5" style="min-height: 600px;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading calendar...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading calendar events...</p>
                    </div>

                    <!-- Calendar Container -->
                    <div id="calendar" style="display: none;"></div>

                    <!-- Error State -->
                    <div id="calendarError" class="alert alert-danger d-none">
                        <h6 class="fw-semibold">Calendar Error</h6>
                        <p class="mb-0">Failed to load calendar events. Please refresh the page or contact support.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Time Restriction Information Modal -->
    <x-confirmation-modal
        modal-id="timeRestrictionModal"
        type="info"
        title="Reservations are only available between 07:00 and 23:00 (opening hours)"
        warning-text="Please select a time slot within our operating hours."
        dismiss-text="OK"
        form-action="#"
    />

    <!-- Reservation Details Modal -->
    <x-reservation-details-modal />

    <!-- Reservation Modal JavaScript -->
    <script src="{{ asset('assets/js/reservation-modal.js') }}"></script>

    <!-- FullCalendar: using local assets from layout (avoid duplicating CDN) -->
    @push('scripts')
        <!-- Locale for DD/MM/YYYY format -->
        <script src="{{ asset('assets/libs/fullcalendar/locales/en-gb.js') }}"></script>
    @endpush


    <!-- Custom FullCalendar Dark Mode Styles -->
    <style>
        /* FullCalendar Dark Mode Weekday Header Fix */
        [data-theme-mode="dark"] .fc-theme-standard .fc-col-header-cell {
            background-color: rgb(var(--body-bg-rgb2)) !important;
            border-color: rgba(255, 255, 255, 0.1) !important;
        }

        [data-theme-mode="dark"] .fc-theme-standard .fc-col-header-cell .fc-col-header-cell-cushion {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* Additional dark mode calendar styling for consistency */
        [data-theme-mode="dark"] .fc-theme-standard .fc-scrollgrid {
            border-color: rgba(255, 255, 255, 0.1) !important;
        }

        [data-theme-mode="dark"] .fc-theme-standard td,
        [data-theme-mode="dark"] .fc-theme-standard th {
            border-color: rgba(255, 255, 255, 0.1) !important;
        }

        [data-theme-mode="dark"] .fc-daygrid-day-number {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        [data-theme-mode="dark"] .fc-daygrid-day.fc-day-today {
            background-color: rgba(var(--primary-rgb), 0.1) !important;
        }

        [data-theme-mode="dark"] .fc .fc-button-primary {
            background-color: rgb(var(--primary-rgb)) !important;
            border-color: rgb(var(--primary-rgb)) !important;
        }

        [data-theme-mode="dark"] .fc .fc-button-primary:hover {
            background-color: rgba(var(--primary-rgb), 0.9) !important;
            border-color: rgba(var(--primary-rgb), 0.9) !important;
        }

        [data-theme-mode="dark"] .fc .fc-toolbar-title {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* SPECIFIC FIX: Target .fc-scrollgrid-section-sticky class causing white background */
        [data-theme-mode="dark"] .fc-scrollgrid-section-sticky {
            background-color: rgb(var(--body-bg-rgb2)) !important;
            background-image: none !important;
            background: rgb(var(--body-bg-rgb2)) !important;
        }

        /* Additional targeting for sticky header elements */
        [data-theme-mode="dark"] .fc-scrollgrid-section-sticky .fc-col-header-cell,
        [data-theme-mode="dark"] .fc-scrollgrid-section-sticky th,
        [data-theme-mode="dark"] .fc-scrollgrid-section-sticky td {
            background-color: rgb(var(--body-bg-rgb2)) !important;
            border-color: rgba(255, 255, 255, 0.1) !important;
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* Legend dark mode styling */
        [data-theme-mode="dark"] .calendar-legend {
            background-color: rgb(var(--light-rgb)) !important;
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* Ensure legend text is visible in dark mode */
        [data-theme-mode="dark"] .calendar-legend h6,
        [data-theme-mode="dark"] .calendar-legend span {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        /* Event borders for improved visual distinction */
        .fc-event {
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: 4px !important;
        }

        /* Enhanced borders for dark mode */
        [data-theme-mode="dark"] .fc-event {
            border: 1px solid rgba(255, 255, 255, 0.4) !important;
        }

        /* Specific border colors for different event types to improve contrast */
        .fc-event[style*="background-color: rgb(251, 191, 36)"],
        .fc-event[style*="background-color:#fbbf24"] {
            /* Pending events (yellow) - darker border for better contrast */
            border-color: rgba(217, 119, 6, 0.6) !important;
        }

        .fc-event[style*="background-color: rgb(35, 183, 229)"],
        .fc-event[style*="background-color:#23b7e5"] {
            /* Confirmed events (light blue) - darker blue border */
            border-color: rgba(14, 116, 144, 0.6) !important;
        }

        .fc-event[style*="background-color: rgb(140, 144, 151)"],
        .fc-event[style*="background-color:#8c9097"] {
            /* Completed/Past events (gray) - darker gray border */
            border-color: rgba(75, 85, 99, 0.6) !important;
        }

        /* Field-based event border colors for better contrast */
        .fc-event[style*="background-color: rgb(78, 172, 76)"],
        .fc-event[style*="background-color:#4eac4c"] {
            /* Field color: Green - darker green border */
            border-color: rgba(34, 120, 52, 0.6) !important;
        }

        .fc-event[style*="background-color: rgb(133, 133, 133)"],
        .fc-event[style*="background-color:#858585"] {
            /* Field color: Gray - darker gray border */
            border-color: rgba(75, 75, 75, 0.6) !important;
        }

        .fc-event[style*="background-color: rgb(192, 166, 129)"],
        .fc-event[style*="background-color:#c0a681"] {
            /* Field color: Beige/Tan - darker brown border */
            border-color: rgba(139, 119, 89, 0.6) !important;
        }

        .fc-event[style*="background-color: rgb(214, 137, 16)"],
        .fc-event[style*="background-color:#D68910"] {
            /* Field color: Orange - darker orange border */
            border-color: rgba(161, 103, 12, 0.6) !important;
        }

        .fc-event[style*="background-color: rgb(41, 128, 185)"],
        .fc-event[style*="background-color:#2980B9"] {
            /* Field color: Blue - darker blue border */
            border-color: rgba(31, 96, 139, 0.6) !important;
        }

        .fc-event[style*="background-color: rgb(6, 182, 212)"],
        .fc-event[style*="background-color:#06b6d4"] {
            /* Field color: Cyan - darker cyan border */
            border-color: rgba(5, 137, 160, 0.6) !important;
        }

        .fc-event[style*="background-color: rgb(249, 115, 22)"],
        .fc-event[style*="background-color:#f97316"] {
            /* Field color: Orange - darker orange border */
            border-color: rgba(187, 86, 17, 0.6) !important;
        }

        .fc-event[style*="background-color: rgb(132, 204, 22)"],
        .fc-event[style*="background-color:#84cc16"] {
            /* Field color: Lime Green - darker lime border */
            border-color: rgba(99, 153, 17, 0.6) !important;
        }

        /* Dark mode specific border adjustments */
        [data-theme-mode="dark"] .fc-event[style*="background-color: rgb(251, 191, 36)"],
        [data-theme-mode="dark"] .fc-event[style*="background-color:#fbbf24"] {
            border-color: rgba(245, 158, 11, 0.8) !important;
        }

        [data-theme-mode="dark"] .fc-event[style*="background-color: rgb(35, 183, 229)"],
        [data-theme-mode="dark"] .fc-event[style*="background-color:#23b7e5"] {
            border-color: rgba(56, 189, 248, 0.8) !important;
        }

        [data-theme-mode="dark"] .fc-event[style*="background-color: rgb(140, 144, 151)"],
        [data-theme-mode="dark"] .fc-event[style*="background-color:#8c9097"] {
            border-color: rgba(156, 163, 175, 0.8) !important;
        }

        /* Dark mode field-based event border colors */
        [data-theme-mode="dark"] .fc-event[style*="background-color: rgb(78, 172, 76)"],
        [data-theme-mode="dark"] .fc-event[style*="background-color:#4eac4c"] {
            /* Field color: Green - lighter green border for dark mode */
            border-color: rgba(110, 200, 108, 0.8) !important;
        }

        [data-theme-mode="dark"] .fc-event[style*="background-color: rgb(133, 133, 133)"],
        [data-theme-mode="dark"] .fc-event[style*="background-color:#858585"] {
            /* Field color: Gray - lighter gray border for dark mode */
            border-color: rgba(165, 165, 165, 0.8) !important;
        }

        [data-theme-mode="dark"] .fc-event[style*="background-color: rgb(192, 166, 129)"],
        [data-theme-mode="dark"] .fc-event[style*="background-color:#c0a681"] {
            /* Field color: Beige/Tan - lighter tan border for dark mode */
            border-color: rgba(220, 190, 153, 0.8) !important;
        }

        [data-theme-mode="dark"] .fc-event[style*="background-color: rgb(214, 137, 16)"],
        [data-theme-mode="dark"] .fc-event[style*="background-color:#D68910"] {
            /* Field color: Orange - lighter orange border for dark mode */
            border-color: rgba(242, 161, 46, 0.8) !important;
        }

        [data-theme-mode="dark"] .fc-event[style*="background-color: rgb(41, 128, 185)"],
        [data-theme-mode="dark"] .fc-event[style*="background-color:#2980B9"] {
            /* Field color: Blue - lighter blue border for dark mode */
            border-color: rgba(71, 158, 215, 0.8) !important;
        }

        [data-theme-mode="dark"] .fc-event[style*="background-color: rgb(6, 182, 212)"],
        [data-theme-mode="dark"] .fc-event[style*="background-color:#06b6d4"] {
            /* Field color: Cyan - lighter cyan border for dark mode */
            border-color: rgba(36, 212, 242, 0.8) !important;
        }

        [data-theme-mode="dark"] .fc-event[style*="background-color: rgb(249, 115, 22)"],
        [data-theme-mode="dark"] .fc-event[style*="background-color:#f97316"] {
            /* Field color: Orange - lighter orange border for dark mode */
            border-color: rgba(255, 145, 52, 0.8) !important;
        }

        [data-theme-mode="dark"] .fc-event[style*="background-color: rgb(132, 204, 22)"],
        [data-theme-mode="dark"] .fc-event[style*="background-color:#84cc16"] {
            /* Field color: Lime Green - lighter lime border for dark mode */
            border-color: rgba(162, 234, 52, 0.8) !important;
        }

        /* Hover effect for reservations with enhanced border visibility */
        .fc-event:hover {
            opacity: 0.9;
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
            border-width: 1px !important;
        }

        /* Dark mode hover effect */
        [data-theme-mode="dark"] .fc-event:hover {
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4) !important;
        }

        /* Color scheme selector styling */
        #colorSchemeSelector {
            transition: all 0.2s ease;
            border: 1px solid #dee2e6;
        }

        #colorSchemeSelector:focus {
            border-color: rgb(var(--primary-rgb));
            box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
        }

        /* Dark mode color scheme selector */
        [data-theme-mode="dark"] #colorSchemeSelector {
            background-color: rgb(var(--body-bg-rgb2));
            border-color: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
        }

        [data-theme-mode="dark"] #colorSchemeSelector:focus {
            border-color: rgb(var(--primary-rgb));
            box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
        }

        /* Legend improvements */
        .calendar-legend {
            transition: all 0.3s ease;
        }

        .calendar-legend .badge {
            min-width: 16px;
            min-height: 16px;
            border-radius: 3px;
        }

        /* Responsive legend layout */
        @media (max-width: 768px) {
            .calendar-legend .d-flex {
                flex-direction: column;
                gap: 1rem !important;
            }

            .calendar-legend .flex-shrink-0,
            .calendar-legend .flex-grow-1 {
                flex: none;
            }
        }

        /* Past reservation styling for color scheme */
        .fc-event.past-reservation-scheme {
            opacity: 0.5 !important;
            position: relative;
        }

        /* Add subtle pattern overlay to further distinguish past reservations */
        .fc-event.past-reservation-scheme::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 2px,
                rgba(255, 255, 255, 0.1) 2px,
                rgba(255, 255, 255, 0.1) 4px
            );
            pointer-events: none;
            border-radius: inherit;
        }

        /* Dark mode adjustments for past reservation pattern */
        [data-theme-mode="dark"] .fc-event.past-reservation-scheme::after {
            background-image: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 2px,
                rgba(0, 0, 0, 0.2) 2px,
                rgba(0, 0, 0, 0.2) 4px
            );
        }
    </style>

    <script>
        // Cache frequently accessed DOM elements
        const DOMCache = {
            calendar: null,
            fieldFilter: null,
            colorSchemeSelector: null,
            legendContent: null,
            calendarLoading: null,
            calendarError: null,
            timeRestrictionModal: null,
            csrfToken: null,

            // Initialize cache
            init() {
                this.calendar = document.getElementById('calendar');
                this.fieldFilter = document.getElementById('fieldFilter');
                this.colorSchemeSelector = document.getElementById('colorSchemeSelector');
                this.legendContent = document.getElementById('legendContent');
                this.calendarLoading = document.getElementById('calendarLoading');
                this.calendarError = document.getElementById('calendarError');
                this.timeRestrictionModal = document.getElementById('timeRestrictionModal');
                this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

                return this.calendar && this.fieldFilter && this.colorSchemeSelector && this.legendContent && this.calendarLoading && this.calendarError;
            },

            // Utility functions for common operations
            showLoading() {
                this.calendarLoading.style.display = 'block';
                this.calendar.style.display = 'none';
                this.calendarError.classList.add('d-none');
            },

            showCalendar() {
                this.calendarLoading.style.display = 'none';
                this.calendar.style.display = 'block';
                this.calendarError.classList.add('d-none');
            },

            showError() {
                this.calendarLoading.style.display = 'none';
                this.calendarError.classList.remove('d-none');
            }
        };

        // Color scheme management
        const ColorSchemeManager = {
            // Field color palette - distinct, accessible colors
            fieldColors: {
                @foreach ($fields as $field)
                {{ $field->id }}: "{{ ['#4eac4c', '#858585', '#c0a681', '#D68910', '#2980B9', '#06b6d4', '#f97316', '#84cc16'][($field->id - 1) % 8] }}", // {{ $field->name }}
                @endforeach
            },

            // Status color palette
            statusColors: {
                'Pending': '#fbbf24',     // yellow
                'Confirmed': '#23b7e5',   // light blue
                'Cancelled': '#ef4444',   // red
                'Complete': '#8c9097',    // gray
                'Completed': '#8c9097',   // gray
                'past': '#8c9097'         // gray for past reservations
            },

            // Get current color scheme from localStorage
            getCurrentScheme() {
                return localStorage.getItem('calendar-color-scheme') || 'status';
            },

            // Set color scheme and save to localStorage
            setColorScheme(scheme) {
                localStorage.setItem('calendar-color-scheme', scheme);
                this.updateLegend(scheme);
            },

            // Update legend based on color scheme
            updateLegend(scheme) {
                const legendContent = DOMCache.legendContent;
                if (!legendContent) return;

                if (scheme === 'status') {
                    legendContent.innerHTML = `
                        <div class="d-flex align-items-center">
                            <span class="badge" style="background-color: #fbbf24;">&nbsp;</span>
                            <span class="ms-2">Pending</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge" style="background-color: #23b7e5;">&nbsp;</span>
                            <span class="ms-2">Confirmed</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge" style="background-color: #8c9097;">&nbsp;</span>
                            <span class="ms-2">Completed (Past Reservations)</span>
                        </div>
                    `;
                } else if (scheme === 'field') {
                    const fieldLegendItems = [];
                    @foreach ($fields as $field)
                    fieldLegendItems.push(`
                        <div class="d-flex align-items-center">
                            <span class="badge" style="background-color: {{ ['#4eac4c', '#858585', '#c0a681', '#D68910', '#2980B9', '#06b6d4', '#f97316', '#84cc16'][($field->id - 1) % 8] }};">&nbsp;</span>
                            <span class="ms-2">{{ $field->name }}</span>
                        </div>
                    `);
                    @endforeach
                    legendContent.innerHTML = fieldLegendItems.join('');
                }
            },

            // Initialize color scheme selector
            init() {
                const currentScheme = this.getCurrentScheme();
                DOMCache.colorSchemeSelector.value = currentScheme;
                this.updateLegend(currentScheme);

                // Add event listener for color scheme changes
                DOMCache.colorSchemeSelector.addEventListener('change', (e) => {
                    const newScheme = e.target.value;
                    this.setColorScheme(newScheme);

                    // Refresh calendar events with new color scheme
                    DOMCache.showLoading();
                    if (window.calendar) {
                        window.calendar.refetchEvents();
                    }
                });
            }
        };

        // Initialize calendar with Moment Timezone plugin
        function initializeCalendarWhenReady() {
            console.log('=== FullCalendar Moment Timezone Implementation ===');

            // Step 0: Initialize DOM cache and verify elements are ready
            if (!DOMCache.init()) {
                console.log('DOM elements not ready, retrying in 100ms...');
                setTimeout(initializeCalendarWhenReady, 100);
                return;
            }

            // Step 1: Verify all required dependencies are available
            console.log('1. Dependency Availability:');
            const dependencies = {
                FullCalendar: typeof FullCalendar !== 'undefined',
                moment: typeof moment !== 'undefined',
                momentTimezone: typeof moment !== 'undefined' && typeof moment.tz !== 'undefined',
                FullCalendarMomentTimezone: typeof FullCalendarMomentTimezone !== 'undefined'
            };

            console.log('   - FullCalendar available:', dependencies.FullCalendar);
            console.log('   - Moment available:', dependencies.moment);
            console.log('   - Moment timezone available:', dependencies.momentTimezone);
            console.log('   - FullCalendarMomentTimezone available:', dependencies.FullCalendarMomentTimezone);

            // Check required dependencies
            if (!dependencies.FullCalendar || !dependencies.moment || !dependencies.momentTimezone || !dependencies.FullCalendarMomentTimezone) {
                console.error('Required dependencies not available, cannot proceed');
                return;
            }

            // Step 2: Test Moment Timezone support for America/Curacao
            console.log('2. Moment Timezone Support Test:');
            let curacaoTimeZone = 'America/Curacao';
            let useNowIndicator = true; // Can be enabled with Moment Timezone plugin
            let momentSupportsTimezone = false;
            let useMomentPlugin = true;

            try {
                // Test if Moment.js can handle America/Curacao timezone
                const testMoment = moment().tz('America/Curacao');
                const curacaoTime = testMoment.format('YYYY-MM-DD HH:mm:ss z');
                console.log('   ✓ America/Curacao timezone test successful:', curacaoTime);
                momentSupportsTimezone = true;

                // Calculate current times for verification
                const localTime = moment().format('YYYY-MM-DD HH:mm:ss z');
                const amsterdamTime = moment().tz('Europe/Amsterdam').format('YYYY-MM-DD HH:mm:ss z');
                console.log('   - Local time:', localTime);
                console.log('   - Amsterdam time:', amsterdamTime);
                console.log('   - Curacao time:', curacaoTime);

                // Verify time difference (should be 6 hours in August)
                const amsterdamMoment = moment().tz('Europe/Amsterdam');
                const curacaoMoment = moment().tz('America/Curacao');
                const hoursDiff = amsterdamMoment.utcOffset() - curacaoMoment.utcOffset();
                console.log('   - Time difference (minutes):', hoursDiff);
                console.log('   - Time difference (hours):', hoursDiff / 60);

            } catch (error) {
                console.log('   ✗ America/Curacao timezone test failed:', error.message);
                console.log('   - Will disable Moment Timezone plugin');
                momentSupportsTimezone = false;
                useMomentPlugin = false;
                useNowIndicator = false;
            }

            // Step 3: Determine timezone strategy
            console.log('3. Timezone Strategy:');
            if (useMomentPlugin && momentSupportsTimezone) {
                console.log('   - Using FullCalendar Moment Timezone plugin');
                console.log('   - Timezone:', curacaoTimeZone);
                console.log('   - Moment supports timezone:', momentSupportsTimezone);
                console.log('   - nowIndicator enabled with plugin support');
                console.log('   - Events will display in accurate Curacao time');
            } else {
                console.log('   - Falling back to FullCalendar native timezone support');
                console.log('   - Timezone:', curacaoTimeZone);
                console.log('   - nowIndicator disabled for safety');
                console.log('   - Events may display in UTC (fallback behavior)');
            }

            // Step 4: Build calendar configuration with Moment Timezone plugin
            console.log('4. Building Calendar Configuration:');

            const calendarConfig = {
                initialView: 'dayGridMonth',
                // Use British English locale for DD/MM/YYYY date format
                locale: 'en-gb',
                // nowIndicator enabled with Moment Timezone plugin support
                nowIndicator: useNowIndicator,
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                height: 'auto',
                // Use America/Curacao timezone with Moment Timezone plugin
                timeZone: curacaoTimeZone,
                eventDisplay: 'block',
                displayEventTime: true,
                weekNumbers: true,
                weekNumberCalculation: 'ISO',
                navLinks: true,
                eventTimeFormat: {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                },
                editable: false,
                selectable: true,
                events: function(fetchInfo, successCallback, failureCallback) {
                    const fieldId = DOMCache.fieldFilter.value;
                    const colorScheme = ColorSchemeManager.getCurrentScheme();
                    const url = new URL('{{ route('calendar.events') }}');
                    url.searchParams.append('start', fetchInfo.startStr);
                    url.searchParams.append('end', fetchInfo.endStr);
                    url.searchParams.append('color_scheme', colorScheme);
                    if (fieldId) {
                        url.searchParams.append('field_id', fieldId);
                    }

                    console.log('Fetching calendar events from:', url.toString());

                    fetch(url)
                        .then(response => {
                            console.log('Calendar events response status:', response.status);
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('Calendar events data received:', data);
                            DOMCache.showCalendar();
                            successCallback(data);
                        })
                        .catch(error => {
                            console.error('Calendar events fetch error:', error);
                            DOMCache.showError();
                            failureCallback(error);
                        });
                },
                eventClick: function(info) {
                    // Prevent default navigation
                    info.jsEvent.preventDefault();

                    // Open reservation details in modal
                    openReservationModal(info.event.extendedProps.reservation_id);
                },
                dateClick: function(info) {
                    // Get the current view type
                    const currentView = calendar.view.type;

                    // Month view behavior: navigate to day view for the clicked date
                    if (currentView === 'dayGridMonth') {
                        // Switch to day view for the clicked date
                        calendar.changeView('timeGridDay', info.dateStr);
                    }
                    // Week/Day view behavior: open reservation creation screen
                    else if (currentView === 'timeGridWeek' || currentView === 'timeGridDay') {
                        // Check if time is included (time grid views)
                        if (info.dateStr.includes('T')) {
                            const [date, time] = info.dateStr.split('T');

                            // Validate time slot is within operating hours (07:00 - 23:00)
                            if (!isWithinOperatingHours(time)) {
                                // Show time restriction modal
                                showTimeRestrictionModal();
                                return;
                            }

                            // Time is valid, proceed with reservation creation
                            const url = `/reservations/create?date=${encodeURIComponent(date)}&time=${encodeURIComponent(time)}`;
                            window.location.href = url;
                        } else {
                            // No specific time, redirect to reservation creation page with date only
                            const url = '/reservations/create?date=' + encodeURIComponent(info.dateStr);
                            window.location.href = url;
                        }
                    }
                },
                eventDidMount: function(info) {
                    // Add tooltip with reservation details
                    const props = info.event.extendedProps;
                    const pastIndicator = props.is_past ? '\n(Past Reservation)' : '';
                    info.el.title =
                        `${props.field_name}\nCustomer: ${props.customer_name}\nStatus: ${props.status}\nCost: XCG ${props.total_cost}\nDuration: ${props.duration} hours${pastIndicator}`;

                    // Add CSS class for past reservations
                    if (props.is_past) {
                        // Apply transparency past reservations
                        info.el.classList.add('past-reservation');
                        info.el.classList.add('past-reservation-scheme');
                    }
                },
                eventDrop: function(info) {
                    const event = info.event;

                    fetch(`/calendar/update-reservation/${event.id}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': DOMCache.csrfToken
                            },
                            body: JSON.stringify({
                                start: event.startStr,
                                end: event.endStr || null
                            })
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Failed to update reservation.');
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log(data.message);
                        })
                        .catch(error => {
                            alert('Error updating reservation.');
                            info.revert(); // undo the drag
                        });
                }
            };

            // Add Moment Timezone plugin conditionally
            if (useMomentPlugin && typeof FullCalendarMomentTimezone.default !== 'undefined') {
                try {
                    console.log('   - Adding Moment Timezone plugin...');
                    console.log('   - FullCalendarMomentTimezone.default type:', typeof FullCalendarMomentTimezone.default);

                    calendarConfig.plugins = [FullCalendarMomentTimezone.default];
                    console.log('   ✓ Moment Timezone plugin added to configuration');
                } catch (pluginError) {
                    console.log('   ✗ Failed to add Moment Timezone plugin:', pluginError.message);
                    console.log('   - Stack trace:', pluginError.stack);
                    useMomentPlugin = false;
                    useNowIndicator = false;
                    calendarConfig.nowIndicator = false;
                }
            }

            if (!useMomentPlugin) {
                console.log('   - Using FullCalendar native timezone support (no plugins)');
                // Ensure no plugins array to avoid any plugin-related issues
                delete calendarConfig.plugins;
            }

            console.log('   - Final configuration:', {
                timeZone: calendarConfig.timeZone,
                nowIndicator: calendarConfig.nowIndicator,
                locale: calendarConfig.locale,
                initialView: calendarConfig.initialView,
                hasPlugins: !!calendarConfig.plugins,
                pluginCount: calendarConfig.plugins ? calendarConfig.plugins.length : 0
            });

            // Step 5: Create calendar with Moment Timezone plugin or native support
            console.log('5. Creating FullCalendar Instance:');

            let calendar;
            try {
                console.log(`   - Creating FullCalendar with ${useMomentPlugin ? 'Moment Timezone plugin' : 'native timezone support'}...`);

                calendar = new FullCalendar.Calendar(DOMCache.calendar, calendarConfig);
                console.log('   ✓ FullCalendar instance created successfully');

            } catch (creationError) {
                console.error('   ✗ FullCalendar creation failed:', creationError.message);
                console.error('   - Error name:', creationError.name);
                console.error('   - Stack trace:', creationError.stack);

                // Display error message to user - no fallback needed since app requires JavaScript
                DOMCache.calendar.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: #dc3545; border: 1px solid #dc3545; border-radius: 4px; background-color: #f8d7da;">
                        <h4>Calendar Loading Error</h4>
                        <p>Unable to initialize the calendar. Please refresh the page or contact support.</p>
                        <p><small>Error: ${creationError.message}</small></p>
                    </div>
                `;
                return;
            }

            // Show loading state initially
            DOMCache.showLoading();

            // Store calendar instance globally for color scheme changes
            window.calendar = calendar;

            calendar.render();

            // Initialize color scheme manager
            ColorSchemeManager.init();

            // Initial load complete
            setTimeout(() => {
                DOMCache.showCalendar();
            }, 1000);

            // Refresh calendar when field filter changes
            DOMCache.fieldFilter.addEventListener('change', function() {
                DOMCache.showLoading();
                calendar.refetchEvents();
            });
        }

        // Initialize calendar when DOM is ready - simplified since app requires JavaScript
        document.addEventListener('DOMContentLoaded', initializeCalendarWhenReady);

        /**
         * Check if a time is within operating hours (07:00 - 23:00)
         * Optimized version with cached constants and simplified parsing
         * @param {string} timeStr - Time string in HH:MM format
         * @returns {boolean} - True if within operating hours
         */
        const OPERATING_HOURS = {
            OPENING_MINUTES: 420, // 7 * 60 (07:00 in minutes)
            CLOSING_MINUTES: 1380 // 23 * 60 (23:00 in minutes)
        };

        function isWithinOperatingHours(timeStr) {
            // Extract hour directly from string (more efficient than split)
            const hour = parseInt(timeStr.substring(0, 2), 10);
            const minute = parseInt(timeStr.substring(3, 5), 10);

            // Convert to minutes for comparison
            const timeInMinutes = hour * 60 + minute;

            // Check if time is within operating hours
            return timeInMinutes >= OPERATING_HOURS.OPENING_MINUTES && timeInMinutes < OPERATING_HOURS.CLOSING_MINUTES;
        }

        /**
         * Show the time restriction information modal
         */
        function showTimeRestrictionModal() {
            const modal = new bootstrap.Modal(DOMCache.timeRestrictionModal);
            modal.show();
        }
    </script>
@endsection
